-- Çoklu Branş Üyelik Sistemi Migration Script
-- Bu script mevcut sistemi bozmadan çoklu branş üyelik sistemini destekler
-- G<PERSON>ye uyumlu tasarım ile 100+ salon performansı için optimize edilmiştir

USE [GymProject]
GO

PRINT 'Çoklu Branş Üyelik Sistemi Migration başlatılıyor...'
GO

-- =============================================
-- 1. PERFORMANS İNDEXLERİ EKLEMESİ
-- =============================================

-- Memberships tablosu için composite index (en kritik sorgular için)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Memberships_MemberID_IsActive_EndDate_IsFrozen')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Memberships_MemberID_IsActive_EndDate_IsFrozen] 
    ON [dbo].[Memberships] ([MemberID], [IsActive], [EndDate], [IsFrozen])
    INCLUDE ([MembershipID], [MembershipTypeID], [StartDate], [CompanyID], [UpdatedDate])
    PRINT 'Memberships performans indexi eklendi'
END
GO

-- MembershipTypes tablosu için branch-company index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MembershipTypes_CompanyID_Branch_IsActive')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MembershipTypes_CompanyID_Branch_IsActive] 
    ON [dbo].[MembershipTypes] ([CompanyID], [Branch], [IsActive])
    INCLUDE ([MembershipTypeID], [TypeName], [Day], [Price])
    PRINT 'MembershipTypes branch indexi eklendi'
END
GO

-- Members tablosu için company-active index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Members_CompanyID_IsActive_Name')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Members_CompanyID_IsActive_Name] 
    ON [dbo].[Members] ([CompanyID], [IsActive], [Name])
    INCLUDE ([MemberID], [PhoneNumber], [Gender], [CreationDate])
    PRINT 'Members company-active indexi eklendi'
END
GO

-- =============================================
-- 2. ÇOKLU BRANŞ ÜYELİK KONSOLIDASYONU VIEW
-- =============================================

-- Mevcut view varsa sil
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_MembershipConsolidated')
BEGIN
    DROP VIEW [dbo].[vw_MembershipConsolidated]
    PRINT 'Eski MembershipConsolidated view silindi'
END
GO

-- Çoklu branş üyelik konsolidasyonu için view oluştur
CREATE VIEW [dbo].[vw_MembershipConsolidated]
AS
WITH ActiveMemberships AS (
    -- Aktif üyelikleri al
    SELECT 
        m.MemberID,
        m.CompanyID,
        m.Name,
        m.PhoneNumber,
        m.Gender,
        ms.MembershipID,
        ms.MembershipTypeID,
        ms.StartDate,
        ms.EndDate,
        ms.UpdatedDate,
        mt.Branch,
        mt.TypeName,
        mt.Day as PackageDays,
        mt.Price,
        -- Kalan gün hesaplama
        CASE 
            WHEN ms.StartDate > GETDATE() THEN DATEDIFF(day, GETDATE(), ms.EndDate)
            ELSE DATEDIFF(day, GETDATE(), ms.EndDate)
        END as RemainingDays,
        -- Gelecek tarihli üyelik kontrolü
        CASE WHEN ms.StartDate > GETDATE() THEN 1 ELSE 0 END as IsFutureStartDate,
        -- Branş bazlı sıralama için row number
        ROW_NUMBER() OVER (
            PARTITION BY m.MemberID, mt.Branch 
            ORDER BY ms.UpdatedDate DESC, ms.MembershipID DESC
        ) as BranchRank,
        -- Genel sıralama için row number
        ROW_NUMBER() OVER (
            PARTITION BY m.MemberID 
            ORDER BY ms.UpdatedDate DESC, ms.MembershipID DESC
        ) as OverallRank
    FROM Members m
    INNER JOIN Memberships ms ON m.MemberID = ms.MemberID
    INNER JOIN MembershipTypes mt ON ms.MembershipTypeID = mt.MembershipTypeID
    WHERE 
        m.IsActive = 1 
        AND ms.IsActive = 1 
        AND ms.EndDate > GETDATE() 
        AND ms.IsFrozen = 0
        AND mt.IsActive = 1
),
BranchSummary AS (
    -- Branş bazlı özet bilgiler
    SELECT 
        MemberID,
        CompanyID,
        Branch,
        COUNT(*) as ActivePackageCount,
        SUM(RemainingDays) as TotalRemainingDays,
        MAX(EndDate) as MaxEndDate,
        MIN(StartDate) as MinStartDate,
        MAX(UpdatedDate) as LastUpdateDate
    FROM ActiveMemberships
    GROUP BY MemberID, CompanyID, Branch
)
SELECT 
    am.MemberID,
    am.CompanyID,
    am.Name,
    am.PhoneNumber,
    am.Gender,
    am.MembershipID,
    am.MembershipTypeID,
    am.Branch,
    am.TypeName,
    am.PackageDays,
    am.Price,
    am.StartDate,
    am.EndDate,
    am.UpdatedDate,
    am.RemainingDays,
    am.IsFutureStartDate,
    am.BranchRank,
    am.OverallRank,
    bs.ActivePackageCount,
    bs.TotalRemainingDays as BranchTotalDays,
    bs.MaxEndDate as BranchMaxEndDate,
    bs.LastUpdateDate as BranchLastUpdate,
    -- Çoklu branş kontrolü
    CASE WHEN EXISTS (
        SELECT 1 FROM BranchSummary bs2 
        WHERE bs2.MemberID = am.MemberID 
        AND bs2.CompanyID = am.CompanyID 
        AND bs2.Branch != am.Branch
    ) THEN 1 ELSE 0 END as HasMultipleBranches
FROM ActiveMemberships am
INNER JOIN BranchSummary bs ON am.MemberID = bs.MemberID 
    AND am.CompanyID = bs.CompanyID 
    AND am.Branch = bs.Branch
GO

PRINT 'MembershipConsolidated view oluşturuldu'
GO

-- =============================================
-- 3. BRANŞ BAZLI ÜYE SAYISI VIEW
-- =============================================

-- Mevcut view varsa sil
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_BranchMemberCounts')
BEGIN
    DROP VIEW [dbo].[vw_BranchMemberCounts]
    PRINT 'Eski BranchMemberCounts view silindi'
END
GO

-- Branş bazlı üye sayıları için view
CREATE VIEW [dbo].[vw_BranchMemberCounts]
AS
SELECT 
    CompanyID,
    Branch,
    COUNT(DISTINCT MemberID) as ActiveMemberCount,
    COUNT(*) as ActiveMembershipCount,
    SUM(CASE WHEN RemainingDays <= 7 THEN 1 ELSE 0 END) as ExpiringThisWeek,
    SUM(CASE WHEN RemainingDays <= 30 THEN 1 ELSE 0 END) as ExpiringThisMonth,
    AVG(CAST(RemainingDays as FLOAT)) as AvgRemainingDays
FROM [dbo].[vw_MembershipConsolidated]
WHERE BranchRank = 1  -- Her branş için en güncel kayıt
GROUP BY CompanyID, Branch
GO

PRINT 'BranchMemberCounts view oluşturuldu'
GO

-- =============================================
-- 4. PAKET TİPİ BAZLI SAYILAR VIEW
-- =============================================

-- Mevcut view varsa sil
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_PackageTypeCounts')
BEGIN
    DROP VIEW [dbo].[vw_PackageTypeCounts]
    PRINT 'Eski PackageTypeCounts view silindi'
END
GO

-- Paket tipi bazlı sayılar için view
CREATE VIEW [dbo].[vw_PackageTypeCounts]
AS
SELECT 
    CompanyID,
    Branch,
    TypeName,
    PackageDays,
    COUNT(*) as ActiveCount,
    SUM(CASE WHEN RemainingDays <= 7 THEN 1 ELSE 0 END) as ExpiringThisWeek,
    SUM(CASE WHEN RemainingDays <= 30 THEN 1 ELSE 0 END) as ExpiringThisMonth,
    AVG(CAST(RemainingDays as FLOAT)) as AvgRemainingDays,
    MIN(RemainingDays) as MinRemainingDays,
    MAX(RemainingDays) as MaxRemainingDays
FROM [dbo].[vw_MembershipConsolidated]
GROUP BY CompanyID, Branch, TypeName, PackageDays
GO

PRINT 'PackageTypeCounts view oluşturuldu'
GO

-- =============================================
-- 5. PERFORMANS İZLEME TABLOSU
-- =============================================

-- Performans izleme tablosu oluştur
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'MembershipSystemPerformanceLog')
BEGIN
    CREATE TABLE [dbo].[MembershipSystemPerformanceLog] (
        [LogID] [int] IDENTITY(1,1) NOT NULL,
        [CompanyID] [int] NOT NULL,
        [OperationType] [varchar](50) NOT NULL,
        [ExecutionTimeMs] [int] NOT NULL,
        [RecordCount] [int] NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_MembershipSystemPerformanceLog] PRIMARY KEY CLUSTERED ([LogID])
    )
    
    CREATE NONCLUSTERED INDEX [IX_MembershipSystemPerformanceLog_CompanyID_Date] 
    ON [dbo].[MembershipSystemPerformanceLog] ([CompanyID], [CreatedDate])
    
    PRINT 'MembershipSystemPerformanceLog tablosu oluşturuldu'
END
GO

-- =============================================
-- 6. STORED PROCEDURE'LER
-- =============================================

-- Çoklu branş üyelik listesi için stored procedure
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetMultiBranchMembershipsPaginated')
BEGIN
    DROP PROCEDURE [dbo].[sp_GetMultiBranchMembershipsPaginated]
END
GO

CREATE PROCEDURE [dbo].[sp_GetMultiBranchMembershipsPaginated]
    @CompanyID INT,
    @PageNumber INT = 1,
    @PageSize INT = 20,
    @SearchText NVARCHAR(100) = NULL,
    @Gender INT = NULL,
    @Branch NVARCHAR(50) = NULL,
    @PackageType NVARCHAR(50) = NULL,
    @ShowLevel INT = 1  -- 1: Branş seviyesi, 2: Paket seviyesi
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @StartTime DATETIME = GETDATE()
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize
    
    -- Seviye 1: Branş bazlı görünüm
    IF @ShowLevel = 1
    BEGIN
        WITH FilteredMembers AS (
            SELECT DISTINCT
                MemberID,
                CompanyID,
                Name,
                PhoneNumber,
                Gender,
                Branch,
                BranchTotalDays as RemainingDays,
                BranchMaxEndDate as EndDate,
                BranchLastUpdate as UpdatedDate,
                HasMultipleBranches,
                -- En güncel membership bilgisi için
                (SELECT TOP 1 MembershipID FROM vw_MembershipConsolidated vc2 
                 WHERE vc2.MemberID = vc.MemberID AND vc2.Branch = vc.Branch 
                 ORDER BY UpdatedDate DESC, MembershipID DESC) as LatestMembershipID
            FROM vw_MembershipConsolidated vc
            WHERE 
                CompanyID = @CompanyID
                AND BranchRank = 1  -- Her branş için en güncel kayıt
                AND (@SearchText IS NULL OR Name LIKE '%' + @SearchText + '%' OR PhoneNumber LIKE '%' + @SearchText + '%')
                AND (@Gender IS NULL OR Gender = @Gender)
                AND (@Branch IS NULL OR Branch = @Branch)
        )
        SELECT 
            *,
            COUNT(*) OVER() as TotalCount
        FROM FilteredMembers
        ORDER BY UpdatedDate DESC, Name
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY
    END
    -- Seviye 2: Paket bazlı görünüm
    ELSE
    BEGIN
        WITH FilteredMemberships AS (
            SELECT 
                MemberID,
                CompanyID,
                Name,
                PhoneNumber,
                Gender,
                MembershipID,
                Branch,
                TypeName,
                PackageDays,
                RemainingDays,
                StartDate,
                EndDate,
                UpdatedDate,
                HasMultipleBranches
            FROM vw_MembershipConsolidated
            WHERE 
                CompanyID = @CompanyID
                AND (@SearchText IS NULL OR Name LIKE '%' + @SearchText + '%' OR PhoneNumber LIKE '%' + @SearchText + '%')
                AND (@Gender IS NULL OR Gender = @Gender)
                AND (@Branch IS NULL OR Branch = @Branch)
                AND (@PackageType IS NULL OR TypeName = @PackageType)
        )
        SELECT 
            *,
            COUNT(*) OVER() as TotalCount
        FROM FilteredMemberships
        ORDER BY UpdatedDate DESC, MembershipID DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY
    END
    
    -- Performans logla
    DECLARE @ExecutionTime INT = DATEDIFF(MILLISECOND, @StartTime, GETDATE())
    INSERT INTO MembershipSystemPerformanceLog (CompanyID, OperationType, ExecutionTimeMs, RecordCount)
    VALUES (@CompanyID, 'GetMultiBranchMembershipsPaginated', @ExecutionTime, @@ROWCOUNT)
END
GO

PRINT 'sp_GetMultiBranchMembershipsPaginated stored procedure oluşturuldu'
GO

-- =============================================
-- 7. BRANŞ VE PAKET FİLTRE LİSTESİ PROCEDURE
-- =============================================

IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetBranchAndPackageFilters')
BEGIN
    DROP PROCEDURE [dbo].[sp_GetBranchAndPackageFilters]
END
GO

CREATE PROCEDURE [dbo].[sp_GetBranchAndPackageFilters]
    @CompanyID INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Branş listesi ve sayıları
    SELECT 
        Branch,
        ActiveMemberCount,
        ActiveMembershipCount,
        ExpiringThisWeek,
        ExpiringThisMonth
    FROM vw_BranchMemberCounts
    WHERE CompanyID = @CompanyID
    ORDER BY Branch
    
    -- Paket tipi listesi ve sayıları
    SELECT 
        Branch,
        TypeName,
        PackageDays,
        ActiveCount,
        ExpiringThisWeek,
        ExpiringThisMonth,
        AvgRemainingDays
    FROM vw_PackageTypeCounts
    WHERE CompanyID = @CompanyID
    ORDER BY Branch, PackageDays
END
GO

PRINT 'sp_GetBranchAndPackageFilters stored procedure oluşturuldu'
GO

PRINT 'Çoklu Branş Üyelik Sistemi Migration tamamlandı!'
PRINT 'Oluşturulan bileşenler:'
PRINT '- Performans indexleri (3 adet)'
PRINT '- vw_MembershipConsolidated (Ana view)'
PRINT '- vw_BranchMemberCounts (Branş sayıları)'
PRINT '- vw_PackageTypeCounts (Paket sayıları)'
PRINT '- MembershipSystemPerformanceLog (Performans izleme)'
PRINT '- sp_GetMultiBranchMembershipsPaginated (Ana sorgu)'
PRINT '- sp_GetBranchAndPackageFilters (Filtre listeleri)'
PRINT 'Sistem 100+ salon için optimize edildi!'
GO
